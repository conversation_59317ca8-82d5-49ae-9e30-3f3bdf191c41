import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:platix/api/api_service.dart';
import 'package:platix/data/models/patient_registration_model.dart';

class CreateBillingController extends GetxController {
  final ApiService _apiService = ApiService();

  // Observables for patient search
  var searchPatientController = TextEditingController();
  var searchQuery = ''.obs;
  var searchResults = <PatientData>[].obs;
  var isSearching = false.obs;
  var selectedPatient = Rxn<PatientData>();

  // Form field controllers
  late TextEditingController firstNameController;
  late TextEditingController lastNameController;
  late TextEditingController patientIdController;
  late TextEditingController ageController;
  late TextEditingController dobController;
  late TextEditingController searchServiceController;
  late TextEditingController priceController;
  late TextEditingController discountController;
  late TextEditingController discountAmountController;
  late TextEditingController totalAmountController;
  late TextEditingController paidAmountController;
  late TextEditingController balanceAmountController;

  @override
  void onInit() {
    super.onInit();
    // Initialize controllers
    firstNameController = TextEditingController();
    lastNameController = TextEditingController();
    patientIdController = TextEditingController();
    ageController = TextEditingController();
    dobController = TextEditingController();
    searchServiceController = TextEditingController();
    priceController = TextEditingController();
    discountController = TextEditingController();
    discountAmountController = TextEditingController();
    totalAmountController = TextEditingController();
    paidAmountController = TextEditingController();
    balanceAmountController = TextEditingController();

    // Debounce search queries
    debounce(searchQuery, (query) {
      if (query.length > 2) {
        searchPatients(query);
      } else {
        searchResults.clear();
      }
    }, time: const Duration(milliseconds: 500));
  }

  @override
  void onClose() {
    // Dispose all controllers
    searchPatientController.dispose();
    firstNameController.dispose();
    lastNameController.dispose();
    patientIdController.dispose();
    ageController.dispose();
    dobController.dispose();
    searchServiceController.dispose();
    priceController.dispose();
    discountController.dispose();
    discountAmountController.dispose();
    totalAmountController.dispose();
    paidAmountController.dispose();
    balanceAmountController.dispose();
    super.onClose();
  }

  Future<void> searchPatients(String query) async {
    try {
      isSearching.value = true;
      final response = await _apiService.getAllRegistrations(search: query);
      searchResults.value = response.data;
    } catch (e) {
      Get.snackbar('Error', 'Failed to search for patients: $e');
    } finally {
      isSearching.value = false;
    }
  }

  void selectPatient(PatientData patient) {
    selectedPatient.value = patient;
    searchPatientController.text = patient.displayName;

    // Populate form fields
    firstNameController.text = patient.firstName ?? '';
    lastNameController.text = patient.lastName ?? '';
    patientIdController.text = patient.patientRegId ?? '';
    ageController.text = patient.age?.toString() ?? '';
    dobController.text = patient.dob ?? '';

    searchResults.clear();
  }
}
