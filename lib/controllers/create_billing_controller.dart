import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:platix/api/api_service.dart';
import 'package:platix/data/models/patient_registration_model.dart';
import 'package:platix/data/models/billing_model.dart';

class CreateBillingController extends GetxController {
  final ApiService _apiService = ApiService();

  // Observables for patient search
  var searchPatientController = TextEditingController();
  var searchQuery = ''.obs;
  var searchResults = <PatientData>[].obs;
  var isSearching = false.obs;
  var selectedPatient = Rxn<PatientData>();

  // Observables for service search
  var serviceSearchQuery = ''.obs;
  var serviceSearchResults = <ServiceModel>[].obs;
  var isServiceSearching = false.obs;
  var selectedService = Rxn<ServiceModel>();
  var allServices = <ServiceModel>[].obs;

  // Form field controllers
  late TextEditingController firstNameController;
  late TextEditingController lastNameController;
  late TextEditingController patientIdController;
  late TextEditingController ageController;
  late TextEditingController dobController;
  late TextEditingController searchServiceController;
  late TextEditingController priceController;
  late TextEditingController discountController;
  late TextEditingController discountAmountController;
  late TextEditingController totalAmountController;
  late TextEditingController paidAmountController;
  late TextEditingController balanceAmountController;

  @override
  void onInit() {
    super.onInit();
    // Initialize controllers
    firstNameController = TextEditingController();
    lastNameController = TextEditingController();
    patientIdController = TextEditingController();
    ageController = TextEditingController();
    dobController = TextEditingController();
    searchServiceController = TextEditingController();
    priceController = TextEditingController();
    discountController = TextEditingController();
    discountAmountController = TextEditingController();
    totalAmountController = TextEditingController();
    paidAmountController = TextEditingController();
    balanceAmountController = TextEditingController();

    // Debounce search queries
    debounce(searchQuery, (query) {
      if (query.length > 1) { // Changed from 2 to 1 for 2+ characters
        searchPatients(query);
      } else {
        searchResults.clear();
      }
    }, time: const Duration(milliseconds: 500));

    // Debounce service search queries
    debounce(serviceSearchQuery, (query) {
      if (query.length > 1) { // 2+ characters for service search
        searchServices(query);
      } else {
        serviceSearchResults.clear();
      }
    }, time: const Duration(milliseconds: 300));

    // Load all services on init
    loadAllServices();
  }

  @override
  void onClose() {
    // Dispose all controllers
    searchPatientController.dispose();
    firstNameController.dispose();
    lastNameController.dispose();
    patientIdController.dispose();
    ageController.dispose();
    dobController.dispose();
    searchServiceController.dispose();
    priceController.dispose();
    discountController.dispose();
    discountAmountController.dispose();
    totalAmountController.dispose();
    paidAmountController.dispose();
    balanceAmountController.dispose();
    super.onClose();
  }

  Future<void> searchPatients(String query) async {
    try {
      isSearching.value = true;
      final response = await _apiService.getAllRegistrations(search: query);
      searchResults.value = response.data;
    } catch (e) {
      Get.snackbar('Error', 'Failed to search for patients: $e');
    } finally {
      isSearching.value = false;
    }
  }

  void selectPatient(PatientData patient) {
    selectedPatient.value = patient;
    searchPatientController.text = patient.displayName;

    // Populate form fields
    firstNameController.text = patient.firstName ?? '';
    lastNameController.text = patient.lastName ?? '';
    patientIdController.text = patient.patientRegId ?? '';
    ageController.text = patient.age?.toString() ?? '';
    dobController.text = patient.dob ?? '';

    searchResults.clear();
  }

  Future<void> loadAllServices() async {
    try {
      final response = await _apiService.getAllServices();
      allServices.value = response.data;
    } catch (e) {
      Get.snackbar('Error', 'Failed to load services: $e');
    }
  }

  void searchServices(String query) {
    if (allServices.isEmpty) {
      loadAllServices();
      return;
    }

    final filteredServices = allServices.where((service) {
      final serviceName = service.serviceName?.toLowerCase() ?? '';
      final searchQuery = query.toLowerCase();
      return serviceName.contains(searchQuery);
    }).toList();

    serviceSearchResults.value = filteredServices;
  }

  void selectService(ServiceModel service) {
    selectedService.value = service;
    searchServiceController.text = service.serviceName ?? '';
    priceController.text = service.price.toString();

    // Auto-calculate total amount if no discount
    if (discountController.text.isEmpty && discountAmountController.text.isEmpty) {
      totalAmountController.text = service.price.toString();
    }

    serviceSearchResults.clear();
  }

  // Method to return filtered patients for CustomSearchField
  Future<List<PatientData>> getFilteredPatients(String query) async {
    if (query.length <= 1) return [];

    try {
      isSearching.value = true;
      final response = await _apiService.getAllRegistrations(search: query);
      return response.data;
    } catch (e) {
      Get.snackbar('Error', 'Failed to search for patients: $e');
      return [];
    } finally {
      isSearching.value = false;
    }
  }

  // Method to return filtered services for CustomSearchField
  Future<List<ServiceModel>> getFilteredServices(String query) async {
    if (query.length <= 1) return [];

    if (allServices.isEmpty) {
      await loadAllServices();
    }

    final filteredServices = allServices.where((service) {
      final serviceName = service.serviceName?.toLowerCase() ?? '';
      final searchQuery = query.toLowerCase();
      return serviceName.contains(searchQuery);
    }).toList();

    return filteredServices;
  }
}
