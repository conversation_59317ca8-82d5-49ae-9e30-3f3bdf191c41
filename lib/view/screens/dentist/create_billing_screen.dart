import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:platix/controllers/create_billing_controller.dart';
import 'package:platix/services/permission_service.dart';
import 'package:platix/utils/app_export.dart';
import 'package:platix/view/widgets/custom_dropdown_button.dart';
import 'package:platix/data/models/patient_registration_model.dart';
import 'package:platix/data/models/billing_model.dart';

enum BillingScreenMode { create, edit, view }

class CreateBillingScreen extends StatelessWidget {
  final bool showSearchOption;
  final BillingScreenMode mode;
  final String? patientName;
  final String? serviceName;
  final String? amount;
  final String? date;

  const CreateBillingScreen({
    super.key,
    this.showSearchOption = true,
    this.mode = BillingScreenMode.create,
    this.patientName,
    this.serviceName,
    this.amount,
    this.date,
  });

  @override
  Widget build(BuildContext context) {
    final CreateBillingController controller = Get.put(CreateBillingController());
    final PermissionService permissionService = PermissionService();
    final bool isReadOnly = mode == BillingScreenMode.view;

    // Set initial values if provided
    if (patientName != null) {
      controller.searchPatientController.text = patientName!;
    }
    if (serviceName != null) {
      controller.searchServiceController.text = serviceName!;
    }
    if (amount != null) {
      final cleanAmount = amount!.replaceAll('₹', '').replaceAll(',', '');
      controller.totalAmountController.text = cleanAmount;
      controller.paidAmountController.text = cleanAmount;
    }

    String getAppBarTitle() {
      switch (mode) {
        case BillingScreenMode.create:
          return 'Create Billing';
        case BillingScreenMode.edit:
          return 'Edit Billing';
        case BillingScreenMode.view:
          return 'View Billing';
      }
    }

    return Scaffold(
      appBar: AppBar(
        title: Text(getAppBarTitle()),
      ),
      body: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (showSearchOption)
                Visibility(
                  visible: permissionService.hasPermission('billing', 'is_list'),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          const Text(
                            'Search Patient',
                            style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                          ),
                          const Text(
                            ' *',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: AppColors.primary,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      CustomSearchField<PatientData>(
                        fetchItems: controller.getFilteredPatients,
                        hintText: 'Search Patient',
                        itemAsString: (patient) => patient.displayName,
                        onSelected: (patient) {
                          if (patient != null) {
                            controller.selectPatient(patient);
                          }
                        },
                        searchController: controller.searchPatientController,
                      ),
                      const SizedBox(height: 20),
                    ],
                  ),
                ),
              const Text(
                'First Name',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              CustomTextFormField(
                controller: controller.firstNameController,
                hintText: 'Enter First Name',
                enabled: !isReadOnly,
              ),
              const SizedBox(height: 20),
              const Text(
                'Last Name',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              CustomTextFormField(
                controller: controller.lastNameController,
                hintText: 'Enter Last Name',
                enabled: !isReadOnly,
              ),
              const SizedBox(height: 20),
              const Text(
                'Patient ID',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              CustomTextFormField(
                controller: controller.patientIdController,
                hintText: 'Enter Patient ID',
                enabled: !isReadOnly,
              ),
              const SizedBox(height: 20),
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'Age',
                          style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                        ),
                        const SizedBox(height: 8),
                        CustomTextFormField(
                          controller: controller.ageController,
                          hintText: '10',
                          enabled: !isReadOnly,
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(width: 20),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'Date Of Birth',
                          style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                        ),
                        const SizedBox(height: 8),
                        CustomTextFormField(
                          controller: controller.dobController,
                          hintText: '09 Feb 2021',
                          prefix: const Icon(Icons.calendar_today),
                          enabled: !isReadOnly,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 20),
              const Text(
                'Gender',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              ),
              Obx(() => Row(
                children: [
                  Radio<String>(
                    value: 'Male',
                    groupValue: controller.selectedPatient.value?.gender,
                    onChanged: (value) {
                      // This should be handled in the controller if editable
                    },
                    activeColor: AppColors.primary,
                  ),
                  const Text('Male'),
                  Radio<String>(
                    value: 'Female',
                    groupValue: controller.selectedPatient.value?.gender,
                    onChanged: (value) {
                      // This should be handled in the controller if editable
                    },
                    activeColor: AppColors.primary,
                  ),
                  const Text('Female'),
                ],
              )),
              const SizedBox(height: 20),
              Visibility(
                visible: permissionService.hasPermission('billing', 'is_list'),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        const Text(
                          'Search Service',
                          style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                        ),
                        const Text(
                          ' *',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: AppColors.primary,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    CustomSearchField<ServiceModel>(
                      fetchItems: controller.getFilteredServices,
                      hintText: 'Search Service Name',
                      itemAsString: (service) => service.serviceName ?? '',
                      onSelected: (service) {
                        if (service != null) {
                          controller.selectService(service);
                        }
                      },
                      searchController: controller.searchServiceController,
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 20),
              const Text(
                'Price',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              CustomTextFormField(
                controller: controller.priceController,
                hintText: 'Enter Price',
                enabled: !isReadOnly,
              ),
              const SizedBox(height: 20),
              Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'Discount (%)',
                          style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                        ),
                        const SizedBox(height: 8),
                        CustomTextFormField(
                          controller: controller.discountController,
                          hintText: '10',
                          enabled: !isReadOnly,
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(width: 20),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'Discount (Amount)',
                          style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                        ),
                        const SizedBox(height: 8),
                        CustomTextFormField(
                          controller: controller.discountAmountController,
                          hintText: '10',
                          enabled: !isReadOnly,
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 20),
              const Text(
                'Total Amount',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              CustomTextFormField(
                controller: controller.totalAmountController,
                hintText: 'Enter Total Amount',
                enabled: !isReadOnly,
              ),
              const SizedBox(height: 20),
              Row(
                children: [
                  const Text(
                    'Paid Amount',
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                  ),
                  const Text(
                    ' *',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: AppColors.primary,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              CustomTextFormField(
                controller: controller.paidAmountController,
                hintText: 'Enter Paid Amount',
                enabled: !isReadOnly,
              ),
              const SizedBox(height: 20),
              const Text(
                'Balance Amount',
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              CustomTextFormField(
                controller: controller.balanceAmountController,
                hintText: 'Enter Balance Amount',
                enabled: !isReadOnly,
              ),
              const SizedBox(height: 20),
              Row(
                children: [
                  const Text(
                    'Payment Source',
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                  ),
                  const Text(
                    ' *',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: AppColors.primary,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              CustomDropdownButton(
                hintText: 'Select Payment Source',
                items: [
                  DropdownMenuItem(
                    value: 'UPI',
                    child: Row(
                      children: [
                        Icon(Icons.payment),
                        SizedBox(width: 8),
                        Text('UPI'),
                      ],
                    ),
                  ),
                  DropdownMenuItem(
                    value: 'Card',
                    child: Row(
                      children: [
                        Icon(Icons.credit_card),
                        SizedBox(width: 8),
                        Text('Card'),
                      ],
                    ),
                  ),
                  DropdownMenuItem(
                    value: 'Cash',
                    child: Row(
                      children: [
                        Icon(Icons.money),
                        SizedBox(width: 8),
                        Text('Cash'),
                      ],
                    ),
                  ),
                ],
                value: null, // This needs to be handled by the controller
                onChanged: isReadOnly ? null : (newValue) {
                  // Handle payment source change in controller
                },
              ),
              const SizedBox(height: 30),
              if (mode != BillingScreenMode.view)
                CustomElevatedButton(
                  onPressed: () {},
                  text: mode == BillingScreenMode.edit ? 'Update' : 'Save',
                  buttonStyle: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primary,
                    foregroundColor: AppColors.white,
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }
}
